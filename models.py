"""Data models for KYC MCP Server"""

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class KYCRequest(BaseModel):
    """Base KYC request model"""
    id_number: Optional[str] = None
    authorization_token: Optional[str] = None


class DocumentVerificationRequest(KYCRequest):
    """Document verification request"""
    dob: Optional[str] = None
    document_type: Optional[str] = None


class BankVerificationRequest(KYCRequest):
    """Bank verification request"""
    ifsc: Optional[str] = None
    ifsc_details: Optional[bool] = None
    upi_id: Optional[str] = None
    mobile_number: Optional[str] = None


class CorporateVerificationRequest(KYCRequest):
    """Corporate verification request"""
    company_name_search: Optional[str] = None
    pan_number: Optional[str] = None


class OCRRequest(BaseModel):
    """OCR request model"""
    file_path: str = Field(..., description="Path to the file to be processed")
    authorization_token: Optional[str] = None
    use_pdf: Optional[bool] = None


class FaceVerificationRequest(BaseModel):
    """Face verification request"""
    selfie_path: Optional[str] = None
    id_card_path: Optional[str] = None
    image_path: Optional[str] = None
    authorization_token: Optional[str] = None


class UtilityRequest(KYCRequest):
    """Utility verification request"""
    operator_code: Optional[str] = None
    phone_number: Optional[str] = None


class FinancialRequest(KYCRequest):
    """Financial verification request"""
    pan_number: Optional[str] = None
    tan_number: Optional[str] = None
    year: Optional[str] = None
    quarter: Optional[str] = None
    type_of_return: Optional[str] = None
    aadhaar_number: Optional[str] = None
    name: Optional[str] = None
    mobile: Optional[str] = None
    consent: Optional[str] = None
    id_type: Optional[str] = None


class LegalRequest(BaseModel):
    """Legal verification request"""
    name: Optional[str] = None
    father_name: Optional[str] = None
    address: Optional[str] = None
    case_type: Optional[str] = None
    state_name: Optional[str] = None
    search_type: Optional[str] = None
    category: Optional[str] = None
    cnr_number: Optional[str] = None
    dob: Optional[str] = None
    nationality: Optional[str] = None
    authorization_token: Optional[str] = None
    id_number: Optional[str] = None
    document_type: Optional[str] = None


class VehicleRequest(KYCRequest):
    """Vehicle verification request"""
    rc_number: Optional[str] = None


class UtilityServiceRequest(BaseModel):
    """Utility service request"""
    email: Optional[str] = None
    name_1: Optional[str] = None
    name_2: Optional[str] = None
    name_type: Optional[str] = None
    mobile: Optional[str] = None
    authorization_token: Optional[str] = None


class KYCResponse(BaseModel):
    """Base KYC response model"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    status_code: Optional[int] = None


class APIError(Exception):
    """Custom API error"""
    def __init__(self, message: str, status_code: Optional[int] = None):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)
