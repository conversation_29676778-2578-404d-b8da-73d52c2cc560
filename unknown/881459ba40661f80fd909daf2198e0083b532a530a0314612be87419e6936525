TAN:
curl --location 'https://kyc-api.surepass.io/api/v1/tan/' \
--header 'Content-Type: application/json' \
--data '{
    "id_number": "RTKT06731E"
}'

Voter ID Text:
curl --location 'https://kyc-api.surepass.io/api/v1/voter-id/voter-id' \
--header 'Content-Type: application/json' \
--header 'Authorization: TOKEN' \
--header 'X-Customer-Id: {{customer_id}}' \
--data '{
	"id_number": "{{voter_id_number}}"
}'

ITR Compliance:
curl --location 'https://kyc-api.surepass.io/api/v1/itr/itr-compliance-check' \
--data '{
    "pan_number": "**********"
}'

OCR GST LUT:
curl --location 'https://kyc-api.surepass.io/api/v1/ocr/gst' \
--form 'file=@"/path/to/file"'

Electricity Bill Details:
curl --location 'https://kyc-api.surepass.io/api/v1/utility/electricity/' \
--data '{
    "id_number": "****************",
    "operator_code": "MH"
}'

Bank Verification:
curl --location 'https://kyc-api.surepass.io/api/v1/bank-verification/' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "id_number": "**************",
    "ifsc": "CNRB0000000",
    "ifsc_details": true
}'

Aadhaar To UAN:
curl --location 'https://kyc-api.surepass.io/api/v1/income/epfo/aadhaar-to-uan' \
--header 'Authorization: Bearer TOKEN' \
--header 'Content-Type: application/json' \
--data '{
    "aadhaar_number": "************"
}'

CKYC Search:
curl --location 'https://kyc-api.surepass.io/api/v1/ckyc/search' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "id_number": "**********",
    "document_type": "PAN"
}'

GSTIN By PAN:
curl --location 'https://kyc-api.surepass.io/api/v1/corporate/gstin-by-pan' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "id_number": "**********"
}'

Passport Verification:
curl --location 'https://kyc-api.surepass.io/api/v1/ocr/passport' \
--form 'file=@"/path/to/file"'

Email Check:
curl --location 'https://kyc-api.surepass.io/api/v1/employment/email-check' \
--header 'Authorization: Bearer TOKEN' \
--header 'Content-Type: application/json' \
--data-raw '{
    "email": "<EMAIL>"
}'

OCR License:
curl --location 'https://kyc-api.surepass.io/api/v1/ocr/license' \
--form 'front=@"/path/to/file"'

Name To CIN:
curl --location 'https://kyc-api.surepass.io/api/v1/corporate/name-to-cin-list' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "company_name_search": "Zucol"
}'

OCR ITR:
curl --location 'https://kyc-api.surepass.io/api/v1/ocr/itr-v' \
--form 'file=@"/path/to/file"' \
--form 'use_pdf="true"'

Corporate GSTIN Advanced:
curl --location 'https://kyc-api.surepass.io/api/v1/corporate/gstin-advanced' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "id_number": "27AAACR5055K2Z6"
}'

Telecom:
curl --location 'https://kyc-api.surepass.io/api/v1/telecom/generate-otp' \
--header 'Content-Type: application/json' \
--data '{
	"id_number": "{{phone_number}}"
}'

Court Case Details:
curl --location 'https://kyc-api.surepass.io/api/v1/ecourts/search' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "name": "Gurjeet",
    "father_name": "Jogendra",
    "source": "ecourt",
    "address": "Phatak Road, Opposite Patliputra,Nursing Home, Jharkhad",
    "case_type": "respondent",
    "state_name": "WESTBENGAL",
    "search_type": "individual",
    "category": "civil"
}'

Telecom Verification:
curl --location 'https://kyc-api.surepass.io/api/v1/telecom/telecom-verification' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN’ \' \
--data '{
    "id_number": "**********"
}'

Ecourts Search By Cnr:
curl --location 'https://kyc-api.surepass.io/api/v1/ecourts/ecourt-cnr-search' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "cnr_number": "CHCH0100*********"
}'

Driving License Text:
curl --location 'https://kyc-api.surepass.io/api/v1/driving-license/driving-license' \
--header 'Content-Type: application/json' \
--data '{
	"id_number": "{{license_number}}",
    "dob": "{{dob}}"
}'

Prefill Report V2:
curl --location 'https://kyc-api.surepass.io/api/v1/prefill/prefill-report-v2' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "name": "Rajendra Singh",
    "mobile": "**********"
}'

Passport:
curl --location 'https://kyc-api.surepass.io/api/v1/passport/passport/passport-details' \
--header 'Content-Type: application/json' \
--data '{
	"id_number": "{{file_number}}",
	"dob": "{{dob}}"
}'

RC To Mobile Number:
curl --location 'https://kyc-api.surepass.io/api/v1/rc/rc-to-mobile-number' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "rc_number": "RJ14XJ1234"
}'

OCR GST:
curl --location 'https://kyc-api.surepass.io/api/v1/ocr/gst' \
--form 'file=@"/path/to/file"'

Aadhaar Verification:
curl --location 'https://kyc-api.surepass.io/api/v1/aadhaar-v2/generate-otp' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "id_number": "123457897894"
}'

Director Phone:
curl --location 'https://kyc-api.surepass.io/api/v1/corporate/director-phone' \
--header 'Content-Type: application/json' \
--data '{
    "id_number": "00000000"
}'

Tds Check:
curl --location 'https://kyc-api.surepass.io/api/v1/tan/tds-check' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "tan_number": "DELS01235E",
    "pan_number": "**********",
    "year": "2020",
    "quarter": "Q4",
    "type_of_return": "salary"
}'

Credit Report Details:
curl --location 'https://kyc-api.surepass.io/api/v1/credit-report-v2/fetch-report' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "name": "Vishal Rathore",
    "id_number": "123456785514",
    "id_type": "aadhaar",
    "mobile": "8079012345",
    "consent": "Y"
}'

Pull KRA:
curl --location 'https://kyc-api.surepass.io/api/v1/pull-kra/pull-kra' \
--header 'Content-Type: application/json' \
--data '{
	"id_number": "{{pan_number}}",
	"dob":"{{dob}}"
}'

Corporate GSTIN:
curl --location 'https://kyc-api.surepass.io/api/v1/corporate/gstin' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
	"id_number": "08AKWPJ1234H1ZN"
}'

Corporate CIN:
curl --location 'https://kyc-api.surepass.io/api/v1/corporate/company-details' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "id_number": "U65999MH1995PLC123456"
}'

OCR Voter:
curl --location 'https://kyc-api.surepass.io/api/v1/ocr/voter' \
--form 'file=@"/path/to/file"'

PEP Details:
curl --location 'https://kyc-api.surepass.io/api/v1/pep/match' \
--header 'Content-Type: application/json' \
--data '{
    "name": "narendra modi",
    "dob": "1950-09-17",
    "nationality": "indian",
    "address": "gujrat"
}'

Face Liveness:
curl --location 'https://kyc-api.surepass.io/api/v1/face/face-liveness' \
--form 'file=@"/C:/Users/<USER>/Downloads/face_1.png"'

Face Match:
curl --location 'https://kyc-api.surepass.io/api/v1/face/face-match' \
--form 'selfie=@"/path/to/file"' \
--form 'id_card=@"/path/to/file"'

Bank UPI Verification:
curl --location 'https://kyc-api.surepass.io/api/v1/bank-verification/upi-verification' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data-raw '{
	"upi_id": "*********0@ybl"
}'

Aadhaar Validation:
curl --location 'https://kyc-api.surepass.io/api/v1/aadhaar-validation/aadhaar-validation' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "id_number": "************"
}'

Udyog Aadhaar:
curl --location 'https://kyc-api.surepass.io/api/v1/corporate/udyog-aadhaar' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "id_number": "UDYAM-GJ-25-000000"
}'

E Aadhaar:
curl --location 'https://kyc-api.surepass.io/api/v1/aadhaar/eaadhaar/generate-otp' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "id_number": "************"
}'

PAN To UAN:
curl --location 'https://kyc-api.surepass.io/api/v1/pan/pan-to-uan' \
--header 'Authorization: Bearer TOKEN' \
--header 'Content-Type: application/json' \
--data '{
    "pan_number": "xxxx1234xxxx"
}'

Find UPI ID:
curl --location 'https://kyc-api.surepass.io/api/v1/bank-verification/find-upi-id' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "mobile_number": "*********0"
}'

Aadhaar QR:
curl --location 'https://kyc-api.surepass.io/api/v1/aadhaar/upload/qr' \
--form 'qr_text=""'

Name Match:
curl --location 'https://kyc-api.surepass.io/api/v1/utils/name-matching/' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
	"name_1": "Vishal Rathore",
	"name_2": "Rathore Vishal",
	"name_type": "person"
}'

OCR Passport:
curl --location 'https://kyc-api.surepass.io/api/v1/ocr/passport' \
--form 'file=@"/path/to/file"'

Corporate DIN:
curl --location 'https://kyc-api.surepass.io/api/v1/corporate/din' \
--header 'Content-Type: application/json' \
--data '{
	"id_number": "{{din_number}}"
}'

Face Background Remover:
curl --location 'https://kyc-api.surepass.io/api/v1/face/face-background-remover' \
--header 'Authorization: Bearer TOKEN' \
--form 'file=@"/C:/Users/<USER>/Downloads/WhatsApp Image 2024-02-07 at 17.32.29_f0988364.jpg"'

OCR Document Detect:
curl --location 'https://kyc-api.surepass.io/api/v1/ocr/document-detect' \
--form 'file=@"/path/to/file"'

Mobile To Bank:
curl --location 'https://kyc-api.surepass.io/api/v1/mobile-to-bank-details/verification' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "mobile_no": "**********"
}'

Esic Details:
curl --location 'https://kyc-api.surepass.io/api/v1/esic/esic-v2' \
--data '{
    "id_number": "*********"
}'


OCR Udyog Aadhaar:
curl --location 'https://kyc-api.surepass.io/api/v1/corporate/udyog-aadhaar' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "id_number": "UDYAM-GJ-25-000000"
}'

Upload Aadhaar QR:
curl --location 'https://kyc-api.surepass.io/api/v1/aadhaar/upload/qr' \
--form 'qr_text=""'

RC Text:
curl --location 'https://kyc-api.surepass.io/api/v1/rc/rc-full' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "id_number": "KA12AC3456"
}'

Aadhaar PAN Link Check:
curl --location 'https://kyc-api.surepass.io/api/v1/pan/aadhaar-pan-link-check' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "aadhaar_number": "************"
}'

OCR PAN:
curl --location 'https://kyc-api.surepass.io/api/v1/ocr/pan' \
--form 'file=@"/path/to/file"'

LEI Verification:
curl --location 'https://kyc-api.surepass.io/api/v1/lei-validation/' \
--header 'Content-Type: application/json' \
--data '{
    "lei_code": "*********87"
}'

Face Extract:
curl --location 'https://kyc-api.surepass.io/api/v1/face/face-extract' \
--header 'Authorization: Bearer TOKEN' \
--form 'image=@"/C:/Users/<USER>/Downloads/e1a3b48bab1d39a11a016fa5a7f8207b.jpg"'

OCR Cheque:
curl --location 'https://kyc-api.surepass.io/api/v1/ocr/cheque' \
--header 'Authorization: Bearer TOKEN' \
--form 'file=@"/path/to/file"'




