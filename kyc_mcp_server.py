#!/usr/bin/env python3
"""
KYC Verification MCP Server

A Model Context Protocol server that provides KYC (Know Your Customer) verification tools
using the SurePass API. This server implements various document verification, OCR,
face verification, and other KYC-related services.
"""

import json
import logging

from mcp.server.fastmcp import FastMCP

from kyc_client import <PERSON><PERSON><PERSON>lient
from config import ENDPOINTS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("kyc-mcp-server")

# Create the FastMCP server
mcp = FastMCP("kyc-verification-server")

# Initialize KYC client
kyc_client = KYCClient()

# Define individual tools using FastMCP decorators
@mcp.tool()
async def verify_tan(id_number: str) -> str:
    """Verify TAN (Tax Deduction Account Number)

    Args:
        id_number: TAN number to verify
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(ENDPOINTS["tan"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying TAN: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def verify_voter_id(id_number: str, authorization_token: str = None) -> str:
    """Verify Voter ID

    Args:
        id_number: Voter ID number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(
            ENDPOINTS["voter_id"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying Voter ID: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def verify_driving_license(id_number: str, dob: str) -> str:
    """Verify Driving License

    Args:
        id_number: License number
        dob: Date of birth (YYYY-MM-DD)
    """
    try:
        data = {"id_number": id_number, "dob": dob}
        response = await kyc_client.post_json(ENDPOINTS["driving_license"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying Driving License: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def verify_passport(id_number: str, dob: str) -> str:
    """Verify Passport details

    Args:
        id_number: Passport file number
        dob: Date of birth (YYYY-MM-DD)
    """
    try:
        data = {"id_number": id_number, "dob": dob}
        response = await kyc_client.post_json(ENDPOINTS["passport"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying Passport: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def verify_bank_account(id_number: str, ifsc: str, authorization_token: str = None) -> str:
    """Verify bank account details

    Args:
        id_number: Account number
        ifsc: IFSC code
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number, "ifsc": ifsc, "ifsc_details": True}
        response = await kyc_client.post_json(
            ENDPOINTS["bank_verification"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying Bank Account: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def verify_gstin(id_number: str, authorization_token: str = None) -> str:
    """Verify GSTIN details

    Args:
        id_number: GSTIN number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(
            ENDPOINTS["gstin"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying GSTIN: {str(e)}")
        return f"Error: {str(e)}"

# Additional Document Verification Tools
@mcp.tool()
async def verify_itr_compliance(pan_number: str) -> str:
    """Check ITR compliance for a PAN number

    Args:
        pan_number: PAN number to check compliance for
    """
    try:
        data = {"pan_number": pan_number}
        response = await kyc_client.post_json(ENDPOINTS["itr_compliance"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error checking ITR compliance: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def verify_electricity_bill(id_number: str, operator_code: str) -> str:
    """Verify electricity bill details

    Args:
        id_number: Electricity bill ID number
        operator_code: Operator code (e.g., MH for Maharashtra)
    """
    try:
        data = {"id_number": id_number, "operator_code": operator_code}
        response = await kyc_client.post_json(ENDPOINTS["electricity_bill"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying electricity bill: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def aadhaar_to_uan(aadhaar_number: str, authorization_token: str = None) -> str:
    """Get UAN from Aadhaar number

    Args:
        aadhaar_number: Aadhaar number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"aadhaar_number": aadhaar_number}
        response = await kyc_client.post_json(
            ENDPOINTS["aadhaar_to_uan"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error getting UAN from Aadhaar: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def ckyc_search(id_number: str, document_type: str, authorization_token: str = None) -> str:
    """Search CKYC records

    Args:
        id_number: Document ID number (e.g., PAN)
        document_type: Type of document (e.g., PAN)
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number, "document_type": document_type}
        response = await kyc_client.post_json(
            ENDPOINTS["ckyc_search"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error searching CKYC: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def gstin_by_pan(id_number: str, authorization_token: str = None) -> str:
    """Get GSTIN details by PAN number

    Args:
        id_number: PAN number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(
            ENDPOINTS["gstin_by_pan"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error getting GSTIN by PAN: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def email_check(email: str, authorization_token: str = None) -> str:
    """Check email employment details

    Args:
        email: Email address to check
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"email": email}
        response = await kyc_client.post_json(
            ENDPOINTS["email_check"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error checking email: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def name_to_cin(company_name_search: str, authorization_token: str = None) -> str:
    """Search company CIN by name

    Args:
        company_name_search: Company name to search
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"company_name_search": company_name_search}
        response = await kyc_client.post_json(
            ENDPOINTS["name_to_cin"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error searching CIN by name: {str(e)}")
        return f"Error: {str(e)}"

# Corporate Verification Tools
@mcp.tool()
async def gstin_advanced(id_number: str, authorization_token: str = None) -> str:
    """Get advanced GSTIN details

    Args:
        id_number: GSTIN number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(
            ENDPOINTS["gstin_advanced"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error getting advanced GSTIN details: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def telecom_generate_otp(id_number: str) -> str:
    """Generate OTP for telecom verification

    Args:
        id_number: Phone number
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(ENDPOINTS["telecom_generate_otp"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error generating telecom OTP: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def court_case_search(name: str, father_name: str, address: str, case_type: str,
                           state_name: str, search_type: str, category: str,
                           authorization_token: str = None) -> str:
    """Search court case details

    Args:
        name: Person's name
        father_name: Father's name
        address: Address
        case_type: Type of case (e.g., respondent)
        state_name: State name (e.g., WESTBENGAL)
        search_type: Search type (e.g., individual)
        category: Category (e.g., civil)
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {
            "name": name,
            "father_name": father_name,
            "source": "ecourt",
            "address": address,
            "case_type": case_type,
            "state_name": state_name,
            "search_type": search_type,
            "category": category
        }
        response = await kyc_client.post_json(
            ENDPOINTS["ecourts_search"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error searching court cases: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def telecom_verification(id_number: str, authorization_token: str = None) -> str:
    """Verify telecom details

    Args:
        id_number: Phone number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(
            ENDPOINTS["telecom_verification"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying telecom: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def ecourts_cnr_search(cnr_number: str, authorization_token: str = None) -> str:
    """Search court cases by CNR number

    Args:
        cnr_number: CNR number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"cnr_number": cnr_number}
        response = await kyc_client.post_json(
            ENDPOINTS["ecourts_cnr"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error searching by CNR: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def prefill_report_v2(name: str, mobile: str, authorization_token: str = None) -> str:
    """Generate prefill report v2

    Args:
        name: Person's name
        mobile: Mobile number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"name": name, "mobile": mobile}
        response = await kyc_client.post_json(
            ENDPOINTS["prefill_report"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error generating prefill report: {str(e)}")
        return f"Error: {str(e)}"

# Vehicle Services
@mcp.tool()
async def rc_to_mobile_number(rc_number: str, authorization_token: str = None) -> str:
    """Get mobile number from RC number

    Args:
        rc_number: RC (Registration Certificate) number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"rc_number": rc_number}
        response = await kyc_client.post_json(
            ENDPOINTS["rc_to_mobile"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error getting mobile from RC: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def aadhaar_generate_otp(id_number: str, authorization_token: str = None) -> str:
    """Generate OTP for Aadhaar verification

    Args:
        id_number: Aadhaar number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(
            ENDPOINTS["aadhaar_generate_otp"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error generating Aadhaar OTP: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def director_phone(id_number: str) -> str:
    """Get director phone details

    Args:
        id_number: Director ID number
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(ENDPOINTS["director_phone"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error getting director phone: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def tds_check(tan_number: str, pan_number: str, year: str, quarter: str,
                   type_of_return: str, authorization_token: str = None) -> str:
    """Check TDS details

    Args:
        tan_number: TAN number
        pan_number: PAN number
        year: Year (e.g., 2020)
        quarter: Quarter (e.g., Q4)
        type_of_return: Type of return (e.g., salary)
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {
            "tan_number": tan_number,
            "pan_number": pan_number,
            "year": year,
            "quarter": quarter,
            "type_of_return": type_of_return
        }
        response = await kyc_client.post_json(
            ENDPOINTS["tds_check"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error checking TDS: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def credit_report_details(name: str, id_number: str, id_type: str, mobile: str,
                               consent: str, authorization_token: str = None) -> str:
    """Fetch credit report details

    Args:
        name: Person's name
        id_number: ID number (Aadhaar, etc.)
        id_type: Type of ID (e.g., aadhaar)
        mobile: Mobile number
        consent: Consent (Y/N)
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {
            "name": name,
            "id_number": id_number,
            "id_type": id_type,
            "mobile": mobile,
            "consent": consent
        }
        response = await kyc_client.post_json(
            ENDPOINTS["credit_report"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error fetching credit report: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def pull_kra(id_number: str, dob: str) -> str:
    """Pull KRA details

    Args:
        id_number: PAN number
        dob: Date of birth (YYYY-MM-DD)
    """
    try:
        data = {"id_number": id_number, "dob": dob}
        response = await kyc_client.post_json(ENDPOINTS["pull_kra"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error pulling KRA: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def company_details(id_number: str, authorization_token: str = None) -> str:
    """Get company details by CIN

    Args:
        id_number: CIN number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(
            ENDPOINTS["company_details"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error getting company details: {str(e)}")
        return f"Error: {str(e)}"

# PEP and Legal Services
@mcp.tool()
async def pep_details(name: str, dob: str, nationality: str, address: str) -> str:
    """Check PEP (Politically Exposed Person) details

    Args:
        name: Person's name
        dob: Date of birth (YYYY-MM-DD)
        nationality: Nationality
        address: Address
    """
    try:
        data = {
            "name": name,
            "dob": dob,
            "nationality": nationality,
            "address": address
        }
        response = await kyc_client.post_json(ENDPOINTS["pep_match"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error checking PEP details: {str(e)}")
        return f"Error: {str(e)}"

# Bank and UPI Services
@mcp.tool()
async def bank_upi_verification(upi_id: str, authorization_token: str = None) -> str:
    """Verify UPI ID

    Args:
        upi_id: UPI ID to verify
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"upi_id": upi_id}
        response = await kyc_client.post_json(
            ENDPOINTS["upi_verification"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying UPI: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def aadhaar_validation(id_number: str, authorization_token: str = None) -> str:
    """Validate Aadhaar number

    Args:
        id_number: Aadhaar number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(
            ENDPOINTS["aadhaar_validation"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error validating Aadhaar: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def udyog_aadhaar(id_number: str, authorization_token: str = None) -> str:
    """Verify Udyog Aadhaar

    Args:
        id_number: Udyog Aadhaar number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(
            ENDPOINTS["udyog_aadhaar"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying Udyog Aadhaar: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def e_aadhaar_generate_otp(id_number: str, authorization_token: str = None) -> str:
    """Generate OTP for e-Aadhaar

    Args:
        id_number: Aadhaar number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(
            ENDPOINTS["e_aadhaar"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error generating e-Aadhaar OTP: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def pan_to_uan(pan_number: str, authorization_token: str = None) -> str:
    """Get UAN from PAN number

    Args:
        pan_number: PAN number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"pan_number": pan_number}
        response = await kyc_client.post_json(
            ENDPOINTS["pan_to_uan"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error getting UAN from PAN: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def find_upi_id(mobile_number: str, authorization_token: str = None) -> str:
    """Find UPI ID by mobile number

    Args:
        mobile_number: Mobile number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"mobile_number": mobile_number}
        response = await kyc_client.post_json(
            ENDPOINTS["find_upi_id"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error finding UPI ID: {str(e)}")
        return f"Error: {str(e)}"

# Additional Services
@mcp.tool()
async def name_matching(name_1: str, name_2: str, name_type: str, authorization_token: str = None) -> str:
    """Match two names for similarity

    Args:
        name_1: First name
        name_2: Second name
        name_type: Type of name (e.g., person)
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"name_1": name_1, "name_2": name_2, "name_type": name_type}
        response = await kyc_client.post_json(
            ENDPOINTS["name_matching"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error matching names: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def corporate_din(id_number: str) -> str:
    """Get corporate DIN details

    Args:
        id_number: DIN number
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(ENDPOINTS["din"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error getting DIN details: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def mobile_to_bank_details(mobile_no: str, authorization_token: str = None) -> str:
    """Get bank details from mobile number

    Args:
        mobile_no: Mobile number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"mobile_no": mobile_no}
        response = await kyc_client.post_json(
            ENDPOINTS["mobile_to_bank"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error getting bank details from mobile: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def esic_details(id_number: str) -> str:
    """Get ESIC details

    Args:
        id_number: ESIC number
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(ENDPOINTS["esic_details"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error getting ESIC details: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def rc_full_details(id_number: str, authorization_token: str = None) -> str:
    """Get full RC details

    Args:
        id_number: RC number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"id_number": id_number}
        response = await kyc_client.post_json(
            ENDPOINTS["rc_full"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error getting RC details: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def aadhaar_pan_link_check(aadhaar_number: str, authorization_token: str = None) -> str:
    """Check if Aadhaar is linked to PAN

    Args:
        aadhaar_number: Aadhaar number
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        data = {"aadhaar_number": aadhaar_number}
        response = await kyc_client.post_json(
            ENDPOINTS["aadhaar_pan_link"],
            data,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error checking Aadhaar-PAN link: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def lei_verification(lei_code: str) -> str:
    """Verify LEI code

    Args:
        lei_code: LEI code to verify
    """
    try:
        data = {"lei_code": lei_code}
        response = await kyc_client.post_json(ENDPOINTS["lei_validation"], data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error verifying LEI: {str(e)}")
        return f"Error: {str(e)}"

# OCR and File-based Services
@mcp.tool()
async def ocr_gst_lut(file_path: str) -> str:
    """OCR GST LUT document

    Args:
        file_path: Path to the GST document file
    """
    try:
        files = {"file": file_path}
        response = await kyc_client.post_form(ENDPOINTS["ocr_gst"], files)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error processing GST OCR: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def ocr_passport(file_path: str) -> str:
    """OCR Passport document

    Args:
        file_path: Path to the passport file
    """
    try:
        files = {"file": file_path}
        response = await kyc_client.post_form(ENDPOINTS["ocr_passport"], files)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error processing Passport OCR: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def ocr_license(front_file_path: str) -> str:
    """OCR License document

    Args:
        front_file_path: Path to the front side of license file
    """
    try:
        files = {"front": front_file_path}
        response = await kyc_client.post_form(ENDPOINTS["ocr_license"], files)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error processing License OCR: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def ocr_itr(file_path: str, use_pdf: str = "true") -> str:
    """OCR ITR document

    Args:
        file_path: Path to the ITR file
        use_pdf: Whether to use PDF processing (default: true)
    """
    try:
        files = {"file": file_path}
        data = {"use_pdf": use_pdf}
        response = await kyc_client.post_form(ENDPOINTS["ocr_itr"], files, data)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error processing ITR OCR: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def ocr_voter(file_path: str) -> str:
    """OCR Voter ID document

    Args:
        file_path: Path to the voter ID file
    """
    try:
        files = {"file": file_path}
        response = await kyc_client.post_form(ENDPOINTS["ocr_voter"], files)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error processing Voter ID OCR: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def face_liveness(file_path: str) -> str:
    """Check face liveness

    Args:
        file_path: Path to the face image file
    """
    try:
        files = {"file": file_path}
        response = await kyc_client.post_form(ENDPOINTS["face_liveness"], files)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error checking face liveness: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def face_match(selfie_path: str, id_card_path: str) -> str:
    """Match face between selfie and ID card

    Args:
        selfie_path: Path to the selfie image
        id_card_path: Path to the ID card image
    """
    try:
        files = {"selfie": selfie_path, "id_card": id_card_path}
        response = await kyc_client.post_form(ENDPOINTS["face_match"], files)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error matching faces: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def face_background_remover(file_path: str, authorization_token: str = None) -> str:
    """Remove background from face image

    Args:
        file_path: Path to the face image file
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        files = {"file": file_path}
        response = await kyc_client.post_form(
            ENDPOINTS["face_background_remover"],
            files,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error removing background: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def ocr_document_detect(file_path: str) -> str:
    """Detect document type using OCR

    Args:
        file_path: Path to the document file
    """
    try:
        files = {"file": file_path}
        response = await kyc_client.post_form(ENDPOINTS["ocr_document_detect"], files)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error detecting document: {str(e)}")
        return f"Error: {str(e)}"

# Additional OCR Services
@mcp.tool()
async def face_extract(image_path: str, authorization_token: str = None) -> str:
    """Extract face from image

    Args:
        image_path: Path to the image file
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        files = {"image": image_path}
        response = await kyc_client.post_form(
            ENDPOINTS["face_extract"],
            files,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error extracting face: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def ocr_cheque(file_path: str, authorization_token: str = None) -> str:
    """OCR Cheque document

    Args:
        file_path: Path to the cheque file
        authorization_token: Authorization token (optional if set in environment)
    """
    try:
        files = {"file": file_path}
        response = await kyc_client.post_form(
            ENDPOINTS["ocr_cheque"],
            files,
            authorization_token=authorization_token
        )
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error processing Cheque OCR: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def ocr_pan(file_path: str) -> str:
    """OCR PAN card document

    Args:
        file_path: Path to the PAN card file
    """
    try:
        files = {"file": file_path}
        response = await kyc_client.post_form(ENDPOINTS["ocr_pan"], files)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error processing PAN OCR: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def aadhaar_qr_upload(qr_text: str) -> str:
    """Upload Aadhaar QR text

    Args:
        qr_text: QR text content
    """
    try:
        files = {"qr_text": qr_text}
        response = await kyc_client.post_form(ENDPOINTS["aadhaar_qr"], files)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error uploading Aadhaar QR: {str(e)}")
        return f"Error: {str(e)}"

# Special form-based services that use different parameter names
@mcp.tool()
async def ocr_gst(file_path: str) -> str:
    """OCR GST document

    Args:
        file_path: Path to the GST document file
    """
    try:
        files = {"file": file_path}
        response = await kyc_client.post_form(ENDPOINTS["ocr_gst"], files)
        return json.dumps(response.model_dump(), indent=2)
    except Exception as e:
        logger.error(f"Error processing GST OCR: {str(e)}")
        return f"Error: {str(e)}"

# Add resources
@mcp.resource("kyc://api/documentation")
def get_documentation() -> str:
    """Get KYC API documentation"""
    return """
KYC Verification MCP Server Documentation

This server provides comprehensive access to KYC (Know Your Customer) verification services
through the SurePass API. Available services include:

DOCUMENT VERIFICATION:
- TAN verification and TDS check
- Voter ID verification
- Driving License verification
- Passport verification
- Aadhaar verification, OTP generation, and validation
- PAN card verification and PAN to UAN conversion
- ITR compliance check
- Pull KRA details
- E-Aadhaar OTP generation
- Aadhaar-PAN link check
- Aadhaar QR upload

BANK & FINANCIAL VERIFICATION:
- Bank account verification
- UPI ID verification and discovery
- IFSC code validation
- Mobile to bank details mapping
- Credit report generation
- ESIC details verification

CORPORATE VERIFICATION:
- GSTIN verification (basic and advanced)
- GSTIN by PAN lookup
- Company CIN verification and details
- Director phone details
- Corporate DIN verification
- Udyog Aadhaar verification
- Company name to CIN search

OCR SERVICES:
- PAN card OCR
- Passport OCR
- Driving License OCR
- Voter ID OCR
- GST document OCR
- ITR document OCR
- Cheque OCR
- Document type detection

FACE & BIOMETRIC SERVICES:
- Face liveness detection
- Face matching between selfie and ID
- Face extraction from images
- Background removal from face images

LEGAL & COMPLIANCE:
- CKYC search
- Court case search (eCourts)
- CNR-based court search
- PEP (Politically Exposed Person) matching
- LEI code verification

VEHICLE SERVICES:
- RC (Registration Certificate) full details
- RC to mobile number mapping

UTILITY SERVICES:
- Electricity bill verification
- Telecom OTP generation and verification
- Email employment check
- Name matching utility
- Prefill report generation v2

Usage Notes:
- Each tool requires specific parameters as documented in the tool schemas
- Most tools require authorization tokens for API access (can be set via environment variables)
- File-based tools (OCR, face verification) require valid file paths
- Some tools support both required and optional authorization tokens
- All responses are returned in JSON format for easy parsing
"""

@mcp.resource("kyc://api/endpoints")
def get_endpoints() -> str:
    """Get API endpoints list"""
    return json.dumps(ENDPOINTS, indent=2)


if __name__ == "__main__":
    mcp.run(transport="stdio")
