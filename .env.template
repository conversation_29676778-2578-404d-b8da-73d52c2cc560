# KYC MCP Server Configuration Template
# Copy this file to .env and fill in your actual values

# SurePass API Configuration
SUREPASS_API_TOKEN=your_bearer_token_here
SUREPASS_CUSTOMER_ID=your_customer_id_here

# Optional: API Base URL (default: https://kyc-api.surepass.io/api/v1)
# SUREPASS_BASE_URL=https://kyc-api.surepass.io/api/v1

# Optional: Request timeout in seconds (default: 30)
# REQUEST_TIMEOUT=30

# Optional: Enable debug logging (default: false)
# DEBUG_LOGGING=true
